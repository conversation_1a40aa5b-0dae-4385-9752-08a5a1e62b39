@startuml
title GPU巡检任务状态转换图

[*] --> idle : 系统初始化

state idle {
    idle : 状态: "idle"
    idle : 描述: 无活跃任务
    idle : 操作: 可以启动新任务
    idle : 全局变量: gpuInspectionSummary.Status = "idle"
}

state starting {
    starting : 状态: "starting" 
    starting : 描述: 任务初始化中
    starting : 操作: 创建任务摘要，分配GPU
    starting : 全局变量: gpuInspectionSummary.Status = "running"
}

state running {
    running : 状态: "running"
    running : 描述: 巡检执行中
    running : 操作: 监控任务进度，收集结果
    running : 通道: actionChan[RUNCHAN] != nil
    
    [*] --> task_creating : 开始创建诊断任务
    task_creating --> task_monitoring : 任务创建完成
    task_monitoring --> task_processing : 检测到完成任务
    task_processing --> task_monitoring : 处理完成，继续监控
    task_processing --> round_complete : 当前轮次完成
    round_complete --> task_creating : 开始下一轮次
    round_complete --> [*] : 所有轮次完成
    
    task_creating : 创建诊断任务组
    task_creating : 调用ServicePrechecker.StartPrecheck()
    task_creating : 更新ConfigMap保存任务ID
    
    task_monitoring : 定时器Ticker触发检查
    task_monitoring : 监控运行中的任务状态
    task_monitoring : 等待任务完成信号
    
    task_processing : 获取已完成任务信息
    task_processing : 更新任务摘要统计
    task_processing : 记录异常GPU信息
    task_processing : 从运行列表移除完成任务
    
    round_complete : 删除当前轮次所有任务
    round_complete : 清理ConfigMap
    round_complete : 检查是否还有剩余轮次
}

state finished {
    finished : 状态: "finished"
    finished : 描述: 巡检完成
    finished : 操作: 结果可查询
    finished : 全局变量: gpuInspectionSummary.Status = "finished"
}

state deleting {
    deleting : 状态: "deleting"
    deleting : 描述: 任务删除中
    deleting : 操作: 清理资源
    deleting : 通道: actionChan[DELCHAN] != nil
    
    [*] --> stopping_tasks : 发送停止信号
    stopping_tasks --> cleaning_cache : 清理内存缓存
    cleaning_cache --> cleaning_configmap : 清理ConfigMap
    cleaning_configmap --> deleting_tasks : 删除诊断任务
    deleting_tasks --> deleting_tasks : 重试删除(最多3次)
    deleting_tasks --> [*] : 删除完成
    
    stopping_tasks : 向actionChan[RUNCHAN]发送停止信号
    stopping_tasks : 中断正在运行的巡检循环
    
    cleaning_cache : 清空gpuInspectionSummary
    cleaning_cache : 清空unhealthyGpusInfo
    cleaning_cache : 重置全局状态变量
    
    cleaning_configmap : 调用CleanConfigmap()
    cleaning_configmap : 清空持久化的任务ID列表
    
    deleting_tasks : 调用DeleteAllGpuDiagTasks()
    deleting_tasks : 检查IsNoRunningGpuInspectionTasks()
    deleting_tasks : 使用定时器重试删除
}

idle --> starting : 用户发起巡检请求\nStartGpuInspection()
starting --> running : 任务创建成功\n异步启动QueryAllGpuInspectionTasks()
starting --> idle : 任务创建失败\n返回错误信息

running --> running : 巡检进行中\n定时检查任务状态
running --> finished : 所有轮次完成\nUpdateCompletedGpuInspectionSummary()
running --> deleting : 用户取消任务\nDeleteGpuInspection()

finished --> idle : 清理完成\n等待新任务
finished --> starting : 启动新任务\n检查到已完成状态

deleting --> idle : 删除完成\nactionChan[DELCHAN] = nil
deleting --> deleting : 删除进行中\n重试删除操作

note top of idle
    并发控制:
    - 检查actionChan[RUNCHAN]是否为nil
    - 检查actionChan[DELCHAN]是否为nil
    - 只有在idle状态才能启动新任务
end note

note right of running
    异步执行:
    - go QueryAllGpuInspectionTasks()
    - 使用Ticker定时检查
    - 通过channel控制停止
end note

note bottom of deleting
    容错机制:
    - 最多重试3次删除
    - 使用定时器避免无限等待
    - 异步删除避免阻塞用户操作
end note

@enduml
