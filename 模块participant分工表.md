# GPU巡检系统模块Participant分工表

## 模块架构概览

```
用户 → cwsmportal → cwsm → op-aif-wsm → 外部服务
```

## 详细分工表

| 模块 | Participant | 职责 | 技术栈 | 主要文件 |
|------|-------------|------|--------|----------|
| **cwsmportal** | Angular前端界面 | 用户交互界面 | Angular + TypeScript | `gpu.service.ts`<br/>`main.component.ts`<br/>`result.component.ts` |
| **cwsmportal** | HTTP客户端 | API调用封装 | RxJS + HttpClient | `gpu.service.ts` |
| **cwsm** | InspectiontaskController | API网关代理 | Beego框架 | `inspectiontask_api.go` |
| **cwsm** | 路由和中间件 | 请求转发和认证 | Beego路由 | `router.go` |
| **op-aif-wsm** | GpuInspectionController | HTTP请求处理 | Beego框架 | `gpuinspectioncontroller.go` |
| **op-aif-wsm** | ServiceGpuInspection | 业务逻辑编排 | Go服务层 | `servicegpuinspection.go` |
| **op-aif-wsm** | GpuInspection | 核心巡检逻辑 | Go领域层 | `gpuinspection.go` |
| **op-aif-wsm** | ServicePrechecker | 诊断服务接口 | Go基础设施 | 外部服务调用 |
| **op-aif-wsm** | ServiceDeployment | 部署管理服务 | Kubernetes Client | ConfigMap操作 |
| **op-aif-wsm** | CacheService | 缓存服务 | Go内存缓存 | GPU状态缓存 |
| **op-aif-wsm** | ConfigMap | 持久化存储 | Kubernetes资源 | 任务ID存储 |
| **op-aif-wsm** | 内存状态 | 运行时状态 | Go全局变量 | 任务状态管理 |

## API调用链路

### 启动巡检任务
```
用户 → Angular前端界面 → HTTP客户端 
  ↓
POST /api/v1.0/cwsm/cluster/{clusterId}/apts/inspectiontask
  ↓
InspectiontaskController (cwsm) → 路由和中间件
  ↓
POST /apts/gpuinspectiontask (转发)
  ↓
GpuInspectionController (op-aif-wsm) → ServiceGpuInspection → GpuInspection
  ↓
ServicePrechecker + ServiceDeployment + CacheService
```

### 查询巡检状态
```
用户 → Angular前端界面 → HTTP客户端
  ↓
GET /api/v1.0/cwsm/cluster/{clusterId}/apts/inspectiontask
  ↓
InspectiontaskController (cwsm) → 路由和中间件
  ↓
GET /apts/gpuinspectiontask (转发)
  ↓
GpuInspectionController (op-aif-wsm) → ServiceGpuInspection → GpuInspection
  ↓
内存状态 (读取gpuInspectionSummary + unhealthyGpusInfo)
```

### 删除巡检任务
```
用户 → Angular前端界面 → HTTP客户端
  ↓
DELETE /api/v1.0/cwsm/cluster/{clusterId}/apts/inspectiontask
  ↓
InspectiontaskController (cwsm) → 路由和中间件
  ↓
DELETE /apts/gpuinspectiontask (转发)
  ↓
GpuInspectionController (op-aif-wsm) → ServiceGpuInspection → GpuInspection
  ↓
ServicePrechecker + ServiceDeployment + 内存状态清理
```

## 模块间通信协议

### cwsmportal → cwsm
- **协议**: HTTP/HTTPS
- **格式**: JSON
- **路径**: `/api/v1.0/cwsm/cluster/{clusterId}/apts/inspectiontask`
- **认证**: 通过cwsm中间件处理

### cwsm → op-aif-wsm
- **协议**: HTTP内部调用
- **格式**: JSON
- **路径**: `/apts/gpuinspectiontask`
- **转发**: cwsm作为代理转发请求

### op-aif-wsm内部
- **ServiceGpuInspection**: 业务逻辑编排
- **GpuInspection**: 核心算法实现
- **外部服务**: 通过接口调用

## 数据流向

1. **请求数据**: cwsmportal → cwsm → op-aif-wsm
2. **状态数据**: op-aif-wsm内存状态 ← GpuInspection
3. **持久化数据**: ConfigMap ← ServiceDeployment
4. **缓存数据**: CacheService ← ServiceGpuInspection
5. **响应数据**: op-aif-wsm → cwsm → cwsmportal

## 关键设计原则

1. **单一职责**: 每个participant有明确的职责边界
2. **分层解耦**: 通过接口和服务层解耦
3. **状态管理**: 集中在op-aif-wsm的内存状态中
4. **容错处理**: 每层都有相应的错误处理机制
5. **可扩展性**: 支持多厂商GPU和多种诊断级别

## 部署架构

- **cwsmportal**: 前端静态资源，部署在Web服务器
- **cwsm**: 网关服务，部署在集群入口
- **op-aif-wsm**: 核心服务，部署在Kubernetes集群内

这种分层架构确保了系统的可维护性、可扩展性和高可用性。
