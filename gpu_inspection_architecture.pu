@startuml
!define RECTANGLE class

title GPU巡检系统架构层次图

package "cwsmportal (前端层)" {
    RECTANGLE "Angular前端" as Frontend {
        + GPU巡检界面
        + 状态查询界面
        + 结果展示界面
        + 任务管理界面
    }

    RECTANGLE "HTTP客户端" as HttpClient {
        + createGpuInspectionTask()
        + queryGpuInspectionTask()
        + cancelGpuInspectionTask()
        + getGPUNodes()
    }
}

package "cwsm (网关代理层)" {
    RECTANGLE "InspectiontaskController" as CwsmController {
        + GET /cluster/:clusterId/apts/inspectiontask
        + POST /cluster/:clusterId/apts/inspectiontask
        + DELETE /cluster/:clusterId/apts/inspectiontask
    }

    RECTANGLE "路由和中间件" as CwsmMiddleware {
        + 认证授权
        + 请求转发
        + 错误处理
        + 参数验证
    }
}

package "op-aif-wsm (核心服务层)" {
    RECTANGLE "GpuInspectionController" as Controller {
        + StartInspectionTask()
        + QueryInspectionTask()
        + DeleteInspectionTask()
        + MessageResponse()
    }

    note right of Controller
        职责:
        - HTTP请求处理
        - 参数解析和验证
        - 响应格式化
        - 错误码转换
    end note

    RECTANGLE "ServiceGpuInspection" as Service {
        + StartGpuInspection()
        + QueryGpuInspection()
        + DeleteGpuInspection()
        + GetClusterGpus()
        + CountTotalGpus()
        + GetDiagTypeAndNums()
    }

    note right of Service
        职责:
        - 业务逻辑编排
        - GPU资源管理
        - 任务配置处理
        - 厂商适配
    end note

    RECTANGLE "GpuInspection" as Domain {
        + StartGpuInspection()
        + QueryGpuInspection()
        + DeleteGpuInspection()
        + CreateGpuInspectionTask()
        + QueryAllGpuInspectionTasks()
        + RunInspectionLoop()
        + ProcessGpuInspectionRunningTasks()
    }

    RECTANGLE "状态管理" as StateManager {
        + gpuInspectionSummary
        + unhealthyGpusInfo
        + actionChan
        + swmLock
    }

    note right of Domain
        职责:
        - 核心巡检逻辑
        - 任务生命周期管理
        - 异步执行控制
        - 状态同步
    end note

    RECTANGLE "ServicePrechecker" as Prechecker {
        + StartPrecheck()
        + QueryDiagTask()
        + DeleteDiagTask()
        + GetDiagTaskInfo()
    }

    RECTANGLE "ServiceDeployment" as Deployment {
        + GetAptsCmGpuInspectionTaskids()
        + UpdateAptsCmGpuInspectionTaskids()
    }

    RECTANGLE "CacheService" as Cache {
        + GetClusterUsedGpus()
        + SetClusterUsedGpus()
    }

    RECTANGLE "日志服务" as Logger {
        + Info()
        + Error()
        + Debug()
    }
}

package "存储层 (Storage)" {
    database "ConfigMap" as ConfigMap {
        + 任务ID持久化
        + 配置信息存储
    }
    
    database "内存缓存" as Memory {
        + 任务状态缓存
        + GPU信息缓存
        + 异常信息缓存
    }
    
    database "Kubernetes API" as K8sAPI {
        + GPU设备信息
        + 节点状态信息
        + 资源分配信息
    }
}

package "外部系统 (External)" {
    RECTANGLE "GPU诊断引擎" as DiagEngine {
        + GPU硬件检测
        + 性能测试
        + 故障诊断
        + 结果分析
    }
    
    RECTANGLE "集群管理" as ClusterMgmt {
        + 节点管理
        + 资源调度
        + 状态监控
    }
}

' 依赖关系
Frontend --> HttpClient
HttpClient --> CwsmController
CwsmController --> CwsmMiddleware
CwsmMiddleware --> Controller
Controller --> Service
Service --> Domain
Domain --> StateManager
Domain --> Prechecker
Domain --> Deployment
Service --> Cache
Domain --> Logger

Prechecker --> DiagEngine
Deployment --> ConfigMap
Cache --> Memory
Domain --> Memory
Deployment --> K8sAPI
Cache --> K8sAPI
DiagEngine --> ClusterMgmt

' 数据流
Frontend : 用户交互
HttpClient : HTTP请求
Router : 路由分发
Controller : 请求处理
Service : 业务编排
Domain : 核心逻辑
StateManager : 状态管理
Prechecker : 诊断服务
Deployment : 部署管理
Cache : 缓存服务
ConfigMap : 持久化存储
Memory : 内存存储
DiagEngine : 诊断引擎
ClusterMgmt : 集群管理

note top of Frontend
    cwsmportal技术栈:
    - Angular + TypeScript
    - RxJS响应式编程
    - HTTP客户端
    - 状态管理
end note

note top of CwsmController
    cwsm技术栈:
    - Beego框架
    - 请求代理转发
    - 认证授权
    - 路由管理
end note

note top of Controller
    op-aif-wsm技术栈:
    - Beego框架
    - RESTful API
    - JSON序列化
    - 错误处理
end note

note top of Domain
    设计模式:
    - 单例模式
    - 观察者模式
    - 策略模式
    - 工厂模式
end note

note bottom of ConfigMap
    持久化策略:
    - 任务ID列表
    - 配置参数
    - 状态检查点
    - 容错恢复
end note

@enduml
