@startuml
title GPU巡检流程详细架构图

actor "用户" as User

box "cwsmportal (前端)" 
participant "Angular前端界面" as Frontend
end box

box "cwsm (网关代理)" 
participant "InspectiontaskController" as CwsmController
end box

box "op-aif-wsm (核心服务)" 
participant "GpuInspectionController" as Controller
participant "ServiceGpuInspection" as Service
participant "GpuInspection" as Domain
participant "ServicePrechecker" as Prechecker
participant "ServiceDeployment" as Deployment
participant "CacheService" as Cache
database "ConfigMap" as ConfigMap
database "内存状态" as Memory
end box

== 启动GPU巡检任务 ==
User -> Frontend: 发起GPU巡检请求
Frontend -> CwsmController: POST /api/v1.0/cwsm/cluster/{clusterId}/apts/inspectiontask
note right: 前端通过cwsm网关代理
CwsmController -> Controller: 转发到 POST /apts/gpuinspectiontask
note right: GpuInspectionTaskInfo参数
Controller -> Controller: 解析请求参数
Controller -> Service: StartGpuInspection()

Service -> Cache: 获取集群已使用GPU
Service -> Service: 获取集群GPU列表
Service -> Service: 过滤可用GPU
Service -> Service: 计算诊断类型和数量
Service -> Domain: StartGpuInspection()

Domain -> Domain: 检查任务状态
alt 有运行中任务
    Domain --> Controller: 返回错误"task is running"
else 有删除中任务
    Domain --> Controller: 返回错误"task is deleting"
else 无冲突任务
    Domain -> Domain: 继续执行
end

Domain -> Domain: CleanExistCacheInfo()
Domain -> Deployment: GetAptsCmGpuInspectionTaskids()
Deployment -> ConfigMap: 读取gpuinspection字段
note right: 检查是否有持久化的任务ID

alt ConfigMap不存在
    ConfigMap --> Deployment: 返回NotFound错误
    Deployment -> ConfigMap: 创建默认ConfigMap
    note right: gpuinspection: "[]"
    ConfigMap --> Deployment: 创建成功
    Deployment --> Domain: 返回空任务列表[]
else ConfigMap存在但gpuinspection字段为空
    ConfigMap --> Deployment: 返回"[]"
    Deployment --> Domain: 返回空任务列表[]
else ConfigMap存在且有任务ID
    ConfigMap --> Deployment: 返回JSON格式任务ID列表
    note right: ["task-id-1", "task-id-2"]
    Deployment -> Deployment: JSON反序列化
    Deployment --> Domain: 返回任务ID列表
end

alt 无现有任务
    Domain -> Domain: StartOriginGpuInspectionTask()
else 有现有任务
    Domain -> Domain: StartExistGpuInspectionTask()
    Domain -> Prechecker: 查询现有任务状态
    alt 任务已完成
        Domain -> Domain: 清理并启动新任务
        Domain -> Deployment: CleanConfigmap()
        Deployment -> ConfigMap: 更新gpuinspection为"[]"
        Domain -> Prechecker: DeleteDiagTask(existTaskIds)
    else 任务运行中
        Domain -> Domain: DeleteGpuInspection()
        Domain --> Controller: 返回"task is deleting"
    end
end

== 任务初始化和执行 ==
Domain -> Memory: 初始化任务摘要
note right: gpuInspectionSummary
Domain -> Memory: 更新占用GPU信息
Domain -> Domain: 启动异步任务QueryAllGpuInspectionTasks()

par 异步执行
    Domain -> Domain: CreateGpuInspectionTask()
    Domain -> Domain: SplitDiagTasks()
    note right: 每组最多50个GPU

    loop 为每个任务组
        Domain -> Prechecker: StartPrecheck()
        note right: 创建诊断任务
        Prechecker --> Domain: 返回任务ID
    end

    Domain -> Domain: 收集所有任务ID
    note right: ["apts-gpu-inspection-uuid1-0", "apts-gpu-inspection-uuid1-1"]

    Domain -> Deployment: UpdateAptsCmGpuInspectionTaskids(taskIds)
    Deployment -> Deployment: JSON序列化任务ID列表
    Deployment -> ConfigMap: 更新gpuinspection字段
    note right: 原子更新操作，持久化任务状态
    ConfigMap --> Deployment: 更新成功
    Deployment --> Domain: 持久化完成

    Domain -> Domain: 启动巡检循环

    loop 巡检轮次
        Domain -> Domain: RunInspectionLoop()

        loop 定时检查
            Domain -> Domain: ProcessGpuInspectionRunningTasks()
            Domain -> Domain: CompletedTasks()
            Domain -> Prechecker: GetDiagTaskInfo()
            Domain -> Memory: UpdateGpuInspectionSummary()
            Domain -> Memory: UpdateUnhealthyGpus()

            alt 所有任务完成
                Domain -> Domain: 退出循环
            else 收到停止信号
                Domain -> Domain: 停止巡检
            else 继续监控
                Domain -> Domain: 等待下次检查
            end
        end

        Domain -> Prechecker: DeleteDiagTask(currentRoundTaskIds)
        note right: 删除当前轮次的诊断任务
        Prechecker --> Domain: 删除成功

        Domain -> Deployment: CleanConfigmap()
        Deployment -> ConfigMap: 更新gpuinspection为"[]"
        note right: 清空当前轮次任务ID
        ConfigMap --> Deployment: 清理成功
        Deployment --> Domain: ConfigMap清理完成

        alt 还有剩余轮次
            Domain -> Domain: 开始下一轮次
            note right: 重复创建任务和持久化流程
        else 所有轮次完成
            Domain -> Domain: UpdateCompletedGpuInspectionSummary()
        end
    end
end

Domain --> Controller: 返回成功
Controller --> CwsmController: 返回响应
CwsmController --> Frontend: 返回响应
Frontend --> User: 显示任务启动成功

== 查询巡检状态 ==
User -> Frontend: 查询巡检状态
Frontend -> CwsmController: GET /api/v1.0/cwsm/cluster/{clusterId}/apts/inspectiontask
CwsmController -> Controller: 转发到 GET /apts/gpuinspectiontask
Controller -> Service: QueryGpuInspection()
Service -> Domain: QueryGpuInspection()
Domain -> Memory: 读取gpuInspectionSummary
Domain -> Memory: 读取unhealthyGpusInfo
Domain -> Domain: 构建QueryDiagTask响应
Domain --> Controller: 返回任务状态和异常GPU信息
Controller --> CwsmController: 返回JSON响应
CwsmController --> Frontend: 返回JSON响应
Frontend --> User: 显示巡检进度和结果

== 删除/取消巡检任务 ==
User -> Frontend: 取消巡检任务
Frontend -> CwsmController: DELETE /api/v1.0/cwsm/cluster/{clusterId}/apts/inspectiontask
CwsmController -> Controller: 转发到 DELETE /apts/gpuinspectiontask
Controller -> Service: DeleteGpuInspection()
Service -> Domain: DeleteGpuInspection()

Domain -> Memory: 发送停止信号到actionChan[RUNCHAN]
note right: 中断正在运行的巡检循环

Domain -> Domain: CleanExistCacheInfo()
note right: 清空gpuInspectionSummary和unhealthyGpusInfo

Domain -> Deployment: CleanConfigmap()
Deployment -> ConfigMap: 立即更新gpuinspection为"[]"
note right: 优先清空ConfigMap，确保状态一致性
ConfigMap --> Deployment: 清理成功
Deployment --> Domain: ConfigMap清理完成

par 异步删除实际任务
    Domain -> Domain: DeleteAllGpuInspectionTasks()
    loop 删除重试(最多3次)
        Domain -> Prechecker: DeleteDiagTask(taskIds)
        Prechecker --> Domain: 删除结果
        Domain -> Domain: 检查是否还有运行中任务
        alt 删除成功
            Domain -> Domain: 退出循环
        else 删除失败
            Domain -> Domain: 重试删除
            note right: 使用定时器重试，避免无限等待
        end
    end
end

Domain --> Controller: 返回成功
Controller --> CwsmController: 返回响应
CwsmController --> Frontend: 返回响应
Frontend --> User: 显示删除成功

@enduml