@startuml
title ConfigMap在GPU巡检中的状态流转

actor "用户" as User
participant "GpuInspection" as Domain
participant "ServiceDeployment" as Deployment
database "ConfigMap" as CM
participant "ServicePrechecker" as Prechecker

== 系统启动检查 ==
User -> Domain: 启动GPU巡检任务
Domain -> Deployment: GetAptsCmGpuInspectionTaskids()
Deployment -> CM: 读取gpuinspection字段
CM --> Deployment: 返回任务ID列表

alt ConfigMap不存在
    CM --> Deployment: 返回NotFound错误
    Deployment -> CM: 创建默认ConfigMap
    note right: gpuinspection: "[]"
    CM --> Deployment: 创建成功
    Deployment --> Domain: 返回空任务列表[]
else ConfigMap存在但gpuinspection字段为空
    CM --> Deployment: 返回空字符串或"[]"
    Deployment --> Domain: 返回空任务列表[]
else ConfigMap存在且有任务ID
    CM --> Deployment: 返回JSON格式任务ID列表
    note right: ["task-id-1", "task-id-2"]
    Deployment -> Deployment: JSON反序列化
    Deployment --> Domain: 返回任务ID列表
end

== 处理现有任务 ==
alt 无现有任务
    Domain -> Domain: StartOriginGpuInspectionTask()
    note right: 启动全新的巡检任务
else 有现有任务
    Domain -> Domain: StartExistGpuInspectionTask()
    Domain -> Prechecker: 查询现有任务状态
    alt 现有任务已完成
        Domain -> Domain: 清理并启动新任务
    else 现有任务运行中
        Domain -> Domain: 删除现有任务
        Domain --> User: 返回"task is deleting"
    end
end

== 新任务创建和持久化 ==
Domain -> Domain: CreateGpuInspectionTask()
Domain -> Prechecker: 创建多个诊断任务
loop 为每个任务组
    Prechecker --> Domain: 返回任务ID
end

Domain -> Domain: 收集所有任务ID
note right: ["apts-gpu-inspection-uuid1-0", "apts-gpu-inspection-uuid1-1"]

Domain -> Deployment: UpdateAptsCmGpuInspectionTaskids(taskIds)
Deployment -> Deployment: JSON序列化任务ID列表
Deployment -> CM: 更新gpuinspection字段
note right: 原子更新操作
CM --> Deployment: 更新成功
Deployment --> Domain: 持久化完成

== 任务执行监控 ==
Domain -> Domain: 启动巡检循环
loop 定时检查任务状态
    Domain -> Prechecker: 查询任务完成状态
    Prechecker --> Domain: 返回完成的任务
    Domain -> Domain: 更新内存状态
    note right: 更新gpuInspectionSummary和unhealthyGpusInfo
end

== 轮次完成清理 ==
Domain -> Domain: 当前轮次所有任务完成
Domain -> Prechecker: DeleteDiagTask(taskIds)
Prechecker --> Domain: 删除成功
Domain -> Deployment: CleanConfigmap()
Deployment -> CM: 更新gpuinspection为"[]"
CM --> Deployment: 清理成功

alt 还有剩余轮次
    Domain -> Domain: 开始下一轮次
    note right: 重复创建任务和持久化流程
else 所有轮次完成
    Domain -> Domain: UpdateCompletedGpuInspectionSummary()
    Domain --> User: 巡检任务完成
end

== 用户主动删除任务 ==
User -> Domain: 删除GPU巡检任务
Domain -> Domain: 发送停止信号
Domain -> Domain: CleanExistCacheInfo()
note right: 清理内存状态

Domain -> Deployment: CleanConfigmap()
Deployment -> CM: 立即更新gpuinspection为"[]"
CM --> Deployment: 清理成功

par 异步删除实际任务
    Domain -> Prechecker: DeleteDiagTask(taskIds)
    loop 删除重试(最多3次)
        Prechecker --> Domain: 删除结果
        alt 删除成功
            Domain -> Domain: 退出循环
        else 删除失败
            Domain -> Domain: 重试删除
        end
    end
end

Domain --> User: 删除操作完成

== 系统重启恢复 ==
note over Domain, CM
    系统重启后，通过ConfigMap恢复任务状态
    确保任务不会丢失或重复创建
end note

Domain -> Deployment: GetAptsCmGpuInspectionTaskids()
Deployment -> CM: 读取持久化的任务ID
CM --> Deployment: 返回任务ID列表
Deployment --> Domain: 恢复任务状态
Domain -> Domain: 根据任务状态决定后续操作

== ConfigMap数据格式 ==
note over CM
    ConfigMap结构:
    apiVersion: v1
    kind: ConfigMap
    metadata:
      name: aptsconf
      namespace: admin
    data:
      gpuinspection: '["task-id-1", "task-id-2"]'
      # 其他配置字段...
end note

@enduml
