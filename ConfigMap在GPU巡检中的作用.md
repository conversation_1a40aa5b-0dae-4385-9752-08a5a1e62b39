# ConfigMap在GPU巡检系统中的作用

## 概述

ConfigMap在GPU巡检系统中起到**任务状态持久化**和**系统重启恢复**的关键作用。它是Kubernetes原生的配置管理资源，用于存储GPU巡检任务的关键信息。

## ConfigMap基本信息

### 资源标识
- **名称**: `aptsconf` (由常量 `APTSCMNAME` 定义)
- **命名空间**: `admin` (由常量 `APTSNS` 定义)
- **类型**: Kubernetes ConfigMap资源

### 数据结构
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: aptsconf
  namespace: admin
data:
  codepath: "/workspace/megatron-lm"
  datasets: "[]"
  firststeptimeout: "60"
  gpuinspection: "[]"  # 核心字段：存储GPU巡检任务ID列表
  modelimagename: "swr-plat:2524/admin/image/birensupa-pytorch-llama2_70b:v7.24.40.05"
  modelname: "llama70b"
  nonfirststeptimeout: "15"
  pvcenable: "enable"
  pvcname: "llama70b"
  pvcnamespace: "admin"
  pvcsize: "200Gi"
```

## 核心作用

### 1. 任务ID持久化存储
**字段**: `gpuinspection`
**格式**: JSON字符串数组
**内容**: 当前运行中的GPU巡检任务ID列表

```json
["apts-gpu-inspection-uuid1-0", "apts-gpu-inspection-uuid1-1", "apts-gpu-inspection-uuid2-0"]
```

### 2. 系统重启恢复机制
当op-aif-wsm服务重启时，系统通过ConfigMap恢复之前的任务状态：

```go
// 启动时检查是否有现有任务
cmTaskIds, err := svcDeploy.GetAptsCmGpuInspectionTaskids()
if len(cmTaskIds) == 0 {
    // 启动新任务
    return gi.StartOriginGpuInspectionTask(inspectionInfo)
} else {
    // 处理现有任务
    return gi.StartExistGpuInspectionTask(inspectionInfo, cmTaskIds)
}
```

### 3. 任务状态一致性保证
确保内存状态与持久化状态的一致性，防止任务丢失或重复创建。

## 关键操作

### 1. 读取任务ID列表
```go
func (svcDp *ServiceDeployment) GetAptsCmGpuInspectionTaskids() ([]string, error) {
    aptsCmConf, err := svcDp.GetAptsWorkCm()
    if err != nil {
        return nil, err
    }
    taskIds := make([]string, 0)
    gpuinspection, found := aptsCmConf["gpuinspection"]
    if !found {
        return taskIds, nil  // 返回空列表
    }
    err = json.Unmarshal([]byte(gpuinspection), &taskIds)
    return taskIds, err
}
```

### 2. 更新任务ID列表
```go
func (svcDp *ServiceDeployment) UpdateAptsCmGpuInspectionTaskids(taskIds []string) error {
    // 1. 获取或创建ConfigMap
    // 2. 序列化任务ID列表为JSON
    taskIdsJson, _ := json.Marshal(taskIds)
    configMap.Data["gpuinspection"] = string(taskIdsJson)
    // 3. 更新到Kubernetes
    return cm.UpdateCmGpuinspectionInformation(cmInfo, taskIds)
}
```

### 3. 清理任务ID列表
```go
func (gi *GpuInspection) CleanConfigmap() error {
    logs.Info("clean configmap")
    // 传入空数组，清空gpuinspection字段
    return svcdeploy.GetServiceDeployment().UpdateAptsCmGpuInspectionTaskids(make([]string, 0))
}
```

## 生命周期管理

### 任务创建时
1. 创建诊断任务后，立即将任务ID保存到ConfigMap
2. 确保任务ID持久化成功后才返回成功状态

```go
// 创建任务后立即持久化
tasks := make([]string, 0)
for e := runningTaskIds.Front(); e != nil; e = e.Next() {
    tasks = append(tasks, e.Value.(string))
}
err := svcdeploy.GetServiceDeployment().UpdateAptsCmGpuInspectionTaskids(tasks)
```

### 任务完成时
1. 每轮次完成后清理ConfigMap中的任务ID
2. 所有轮次完成后最终清空ConfigMap

```go
// 轮次完成后清理
err := gi.DeleteAllGpuInspectionTasks()
if err != nil {
    return err
}
return gi.CleanConfigmap()  // 清空ConfigMap
```

### 任务删除时
1. 立即清空ConfigMap中的任务ID
2. 异步删除实际的诊断任务

```go
func (gi *GpuInspection) DeleteGpuInspection() error {
    // 1. 发送停止信号
    if runChan, ok := actionChan[RUNCHAN]; ok && runChan != nil {
        actionChan[RUNCHAN] <- true
    }
    // 2. 清理内存缓存
    gi.CleanExistCacheInfo()
    // 3. 立即清空ConfigMap
    err := gi.CleanConfigmap()
    if err != nil {
        return err
    }
    // 4. 异步删除任务
    go func() {
        if err := gi.DeleteAllGpuInspectionTasks(); err != nil {
            logs.Error("delete GPU inspection tasks failed, err:%v", err)
        }
    }()
    return nil
}
```

## 容错机制

### 1. ConfigMap不存在时自动创建
```go
_, err = cm.GetCmInformation(constant.APTSCMNAME, constant.APTSNS)
if err != nil {
    if err = cm.CreateCmInformation(configMap); err != nil {
        logs.Error("create apts configmap failed, cm info:%v, error:%s\n", configMap, err.Error())
        return err
    }
}
```

### 2. JSON解析失败处理
```go
err = json.Unmarshal([]byte(gpuinspection), &taskIds)
if err != nil {
    return nil, err  // 返回错误，避免数据损坏
}
```

### 3. 并发访问保护
通过Kubernetes的乐观锁机制和ResourceVersion确保并发安全。

## 监控和日志

### 成功操作日志
```go
logs.Info("ConfigMap %s updated successfully in namespace %s\n", cmInfo.Name, cmInfo.Namespace)
logs.Info("clean configmap")
```

### 错误处理日志
```go
logs.Error("update apts configmap failed, error:%s", err.Error())
logs.Error("create apts configmap failed, cm info:%v, error:%s\n", configMap, err.Error())
```

## 设计优势

1. **持久化**: 任务状态不会因服务重启而丢失
2. **一致性**: 确保内存状态与持久化状态同步
3. **可恢复**: 系统重启后能够正确处理现有任务
4. **原子性**: 利用Kubernetes的原子更新机制
5. **可观测**: 可通过kubectl直接查看任务状态

## 使用示例

### 查看当前任务状态
```bash
kubectl get configmap aptsconf -n admin -o jsonpath='{.data.gpuinspection}'
```

### 手动清理任务状态
```bash
kubectl patch configmap aptsconf -n admin --type='merge' -p='{"data":{"gpuinspection":"[]"}}'
```

ConfigMap在GPU巡检系统中扮演着**状态持久化中心**的角色，确保了系统的可靠性和一致性。
