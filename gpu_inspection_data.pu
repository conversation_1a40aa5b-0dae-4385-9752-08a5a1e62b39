@startuml
title GPU巡检数据结构关系图

package "API数据结构" {
    class GpuInspectionTaskInfo {
        +Inspect_type: string
        +Mos: []string
        +Mos_sorted: map[string][]string
        +Mos_occupied: map[string][]string
        +Diag_level: string
        +Inspect_count: int
        +Total_cluster_gpu_number: int
        +Total_test_gpu_number: int
        +Diag_configs: []DiagConf
        +Diag_type: string
        +Inspection_nums: int
        +GPU_card_confs: map[string]GPUCardConf
        +Vendor_diag_level: map[string]string
        +Vendor: string
        +Model: string
    }
    
    class DiagConf {
        +Vendor: string
        +Level: string
        +Config: string
    }
    
    class GPUCardConf {
        +Vendor: string
        +Model: string
    }
    
    class QueryDiagTask {
        +Inspect_type: string
        +Diag_level: string
        +Diag_created: string
        +Diag_finished: string
        +Diag_duration: string
        +Diag_total_count: int
        +Diag_success_count: int
        +Diag_fail_count: int
        +Status: string
        +Result: string
        +Task_result_summary: GpuInspectionSummary
        +Diag_gpu_outputs: []DiagGpuInfo
        +ErrInfo: string
        +ErrInfoZh: string
        +Diag_total_nodes: int
        +DiagType: string
        +InspectionNums: int
    }
    
    class GpuInspectionSummary {
        +TotalCards: int
        +TotalChecks: int
        +TotalUsed: int
        +CheckedCards: int
        +HealthyChecks: int
        +UnhealthyChecks: int
        +FailChecks: int
    }
    
    class DiagGpuInfo {
        +Diag_task_id: string
        +Diag_fail_count: int
        +Diag_name: string
        +GpuUUID: string
        +GpuName: string
        +NodeName: string
        +Vendor: string
        +Model: string
        +Diag_item_outputs: []DiagItemOutput
        +Result: string
        +Diaglevel: string
        +Slot: string
    }
    
    class DiagItemOutput {
        +Diag_gpu_outputs: string
        +Diag_item_result: string
        +Description: string
    }
    
    class DiagTaskInfo {
        +Diag_task_id: string
        +Diag_task_name: string
        +Diag_level: string
        +Diag_created: string
        +Diag_finished: string
        +Status: string
        +Creator: string
        +Task_result_summary: TaskResultSummary
        +Diag_gpu_outputs: []DiagGpuInfo
    }
    
    class TaskResultSummary {
        +Total: int
        +Healthy: int
        +Unhealthy: int
        +Fail: int
    }
}

package "内部状态管理" {
    class GlobalState {
        +gpuInspectionSummary: *QueryDiagTask
        +unhealthyGpusInfo: map[string]*DiagGpuInfo
        +actionChan: map[string]chan bool
    }
    
    class ActionChannels {
        +RUNCHAN: "runchan"
        +DELCHAN: "delchan"
    }
    
    class StatusConstants {
        +INITING: "initing"
        +STARTING: "starting" 
        +RUNNING: "running"
        +FINISHED: "finished"
        +DELETING: "deleting"
        +SUCCESSSTATUS: "success"
        +FAILSTATUS: "failed"
        +IDLESTATUS: "idle"
    }
}

package "服务接口" {
    interface ServicePrechecker {
        +StartPrecheck(name, gpus, configs): taskId
        +QueryDiagTask(taskId): data
        +DeleteDiagTask(taskIds): error
        +GetDiagTaskInfo(taskId): DiagTaskInfo
    }
    
    interface ServiceDeployment {
        +GetAptsCmGpuInspectionTaskids(): []string
        +UpdateAptsCmGpuInspectionTaskids(taskIds): error
    }
    
    interface CacheService {
        +GetClusterUsedGpus(): map[string][]string
    }
}

package "核心业务逻辑" {
    class GpuInspection {
        +swmLock: sync.RWMutex
        +StartGpuInspection(info): error
        +QueryGpuInspection(): map[string]interface{}
        +DeleteGpuInspection(): error
        +CreateGpuInspectionTask(info): *list.List
        +QueryAllGpuInspectionTasks(info): void
        +RunInspectionLoop(taskIds, ticker): string
        +ProcessGpuInspectionRunningTasks(taskIds): void
        +UpdateGpuInspectionSummary(taskInfo): void
        +UpdateUnhealthyGpus(taskInfo): void
        +CleanExistCacheInfo(): void
        +DeleteAllGpuInspectionTasks(): error
    }
    
    class ServiceGpuInspection {
        +StartGpuInspection(info): error
        +QueryGpuInspection(): map[string]interface{}
        +DeleteGpuInspection(): error
        +GetClusterGpus(info): map[string][]string
        +CountTotalGpus(mosSorted): int
        +GetDiagTypeAndNums(info): string, int
    }
}

' 关系定义
GpuInspectionTaskInfo ||--o{ DiagConf : contains
GpuInspectionTaskInfo ||--o{ GPUCardConf : contains
QueryDiagTask ||--|| GpuInspectionSummary : contains
QueryDiagTask ||--o{ DiagGpuInfo : contains
DiagGpuInfo ||--o{ DiagItemOutput : contains
DiagTaskInfo ||--|| TaskResultSummary : contains
DiagTaskInfo ||--o{ DiagGpuInfo : contains

GlobalState ||--|| QueryDiagTask : manages
GlobalState ||--o{ DiagGpuInfo : manages
GlobalState ||--|| ActionChannels : uses

GpuInspection ||--|| GlobalState : manages
GpuInspection ||--|| ServicePrechecker : uses
GpuInspection ||--|| ServiceDeployment : uses
ServiceGpuInspection ||--|| GpuInspection : uses
ServiceGpuInspection ||--|| CacheService : uses

GpuInspection ..> GpuInspectionTaskInfo : processes
GpuInspection ..> QueryDiagTask : returns
GpuInspection ..> DiagTaskInfo : processes
ServiceGpuInspection ..> GpuInspectionTaskInfo : receives

note top of GlobalState
    全局状态变量:
    - gpuInspectionSummary: 当前任务摘要
    - unhealthyGpusInfo: 异常GPU信息映射
    - actionChan: 控制通道映射
    
    并发控制:
    - 使用sync.RWMutex保护共享状态
    - 通过channel实现任务控制
end note

note bottom of GpuInspection
    核心功能:
    1. 任务生命周期管理
    2. 异步任务执行和监控
    3. 状态同步和更新
    4. 资源清理和容错处理
    
    设计模式:
    - 单例模式管理全局状态
    - 观察者模式监控任务状态
    - 策略模式支持多厂商GPU
end note

@enduml
