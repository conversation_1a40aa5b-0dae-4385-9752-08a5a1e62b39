# GPU巡检流程梳理文档

## 概述

本文档基于对代码的深入分析，梳理了GPU巡检系统的完整流程，并提供了多个PlantUML图表来可视化不同层面的系统设计。

## PlantUML图表说明

### 1. inspection.pu - GPU巡检流程详细架构图
**用途**: 展示完整的GPU巡检业务流程
**内容**:
- 启动GPU巡检任务的完整流程
- 任务初始化和执行过程
- 查询巡检状态的流程
- 删除/取消巡检任务的流程
- 各组件间的交互关系

**关键流程**:
1. 用户发起请求 → Controller → Service → Domain
2. 状态检查和冲突处理
3. 任务分割和异步执行
4. 定时监控和结果收集
5. 状态更新和资源清理

### 2. gpu_inspection_state.pu - GPU巡检任务状态转换图
**用途**: 展示任务状态的完整生命周期
**内容**:
- 5个主要状态：idle、starting、running、finished、deleting
- 状态间的转换条件和触发事件
- 每个状态的详细子状态
- 并发控制和容错机制

**关键状态**:
- `idle`: 系统空闲，可接受新任务
- `running`: 任务执行中，包含多个子状态
- `deleting`: 任务删除中，包含清理步骤

### 3. gpu_inspection_data.pu - GPU巡检数据结构关系图
**用途**: 展示系统中所有数据结构的关系
**内容**:
- API层数据结构（请求/响应对象）
- 内部状态管理结构
- 服务接口定义
- 核心业务逻辑类

**关键结构**:
- `GpuInspectionTaskInfo`: 巡检任务配置
- `QueryDiagTask`: 查询响应结构
- `DiagGpuInfo`: GPU诊断信息
- `GlobalState`: 全局状态管理

### 4. gpu_inspection_architecture.pu - GPU巡检系统架构层次图
**用途**: 展示系统的分层架构设计
**内容**:
- 前端层：Angular界面和HTTP客户端
- API网关层：路由和中间件
- 控制器层：HTTP请求处理
- 服务层：业务逻辑编排
- 领域层：核心巡检逻辑
- 基础设施层：外部服务调用
- 存储层：数据持久化
- 外部系统：GPU诊断引擎

## 模块职责分工

### 1. cwsmportal (前端模块)
**职责**: 用户界面和交互
**技术栈**: Angular + TypeScript
**主要组件**:
- `gpu.service.ts`: GPU巡检服务客户端
- `main.component.ts`: 主界面组件
- `result.component.ts`: 结果展示组件
- `gpu-drawer.component.ts`: 配置抽屉组件

**API调用**:
- `POST /api/v1.0/cwsm/cluster/{clusterId}/apts/inspectiontask`
- `GET /api/v1.0/cwsm/cluster/{clusterId}/apts/inspectiontask`
- `DELETE /api/v1.0/cwsm/cluster/{clusterId}/apts/inspectiontask`

### 2. cwsm (网关代理模块)
**职责**: API网关和请求代理
**技术栈**: Beego框架
**主要组件**:
- `InspectiontaskController`: 巡检任务控制器
- 路由配置: `/cluster/:clusterId/apts/inspectiontask`
- 中间件: 认证、授权、请求转发

**功能**:
- 接收前端请求并转发到op-aif-wsm
- 提供统一的API入口
- 处理跨集群的请求路由

### 3. op-aif-wsm (核心服务模块)
**职责**: GPU巡检核心业务逻辑
**技术栈**: Beego + Kubernetes Client
**主要组件**:
- `GpuInspectionController`: HTTP请求处理
- `ServiceGpuInspection`: 业务逻辑编排
- `GpuInspection`: 核心巡检逻辑
- `ServicePrechecker`: 诊断服务接口
- `ServiceDeployment`: 部署管理服务
- `CacheService`: 缓存服务

**API路由**:
- `POST /apts/gpuinspectiontask`
- `GET /apts/gpuinspectiontask`
- `DELETE /apts/gpuinspectiontask`

## 系统核心特性

### 1. 分层架构设计
- **职责分离**: 每层有明确的职责边界
- **依赖倒置**: 高层不依赖低层实现
- **接口抽象**: 通过接口定义服务契约

### 2. 状态管理
- **全局状态**: 使用全局变量管理任务状态
- **并发控制**: 通过mutex和channel保证线程安全
- **状态持久化**: 关键状态保存到ConfigMap

### 3. 异步执行
- **非阻塞**: 巡检任务异步执行，不阻塞用户操作
- **定时监控**: 使用Ticker定时检查任务状态
- **优雅停止**: 通过channel实现任务的优雅停止

### 4. 容错机制
- **重试机制**: 任务创建和删除支持重试
- **状态恢复**: 系统重启后可从ConfigMap恢复状态
- **异常处理**: 完善的错误处理和日志记录

### 5. 可扩展性
- **多厂商支持**: 支持BIREN、NVIDIA、HWJ等GPU厂商
- **多级诊断**: 支持quick、full等不同诊断级别
- **多轮次巡检**: 支持配置多轮次重复巡检

## API接口总结

### 启动巡检任务
```
POST /apts/gpuinspectiontask
Content-Type: application/json

{
  "inspect_type": "inspection",
  "mos": ["gpu-uuid-1", "gpu-uuid-2"],
  "diag_level": "quick",
  "inspect_count": 1,
  "diag_configs": [
    {"vendor": "biren", "level": "quick", "config": ""}
  ]
}
```

### 查询巡检状态
```
GET /apts/gpuinspectiontask
```

### 删除巡检任务
```
DELETE /apts/gpuinspectiontask
```

## 关键代码文件

### cwsmportal模块
1. **前端服务**:
   - `cwsmportal/client/src/client/app/inspection/gpu/gpu.service.ts`
2. **前端组件**:
   - `cwsmportal/client/src/client/app/inspection/gpu/main/main.component.ts`
   - `cwsmportal/client/src/client/app/inspection/gpu/result/result.component.ts`
   - `cwsmportal/client/src/client/app/inspection/gpu/gpu-drawer/gpu-drawer.component.ts`

### cwsm模块
1. **网关控制器**:
   - `cwsm/controllers/inspectiontask_api.go`
2. **路由配置**:
   - `cwsm/routers/router.go`

### op-aif-wsm模块
1. **控制器层**:
   - `op-aif-wsm/controllers/gpuinspectioncontroller/gpuinspectioncontroller.go`
2. **服务层**:
   - `op-aif-wsm/domain/service/servicegpuinspection/servicegpuinspection.go`
3. **领域层**:
   - `op-aif-wsm/domain/inspection/gpu_inspection/gpuinspection.go`
4. **数据结构**:
   - `op-aif-wsm/api/v1/aiworkspace_types.go`
5. **路由配置**:
   - `op-aif-wsm/pkg/routers/router.go`
   - `op-aif-wsm/pkg/constants/url.go`

## 使用建议

1. **查看流程**: 先看`inspection.pu`了解整体流程
2. **理解状态**: 通过`gpu_inspection_state.pu`理解状态转换
3. **掌握数据**: 使用`gpu_inspection_data.pu`了解数据结构
4. **把握架构**: 通过`gpu_inspection_architecture.pu`理解系统架构

## 生成图表命令

使用PlantUML工具生成图表：

```bash
# 生成PNG图片
plantuml -tpng inspection.pu
plantuml -tpng gpu_inspection_state.pu
plantuml -tpng gpu_inspection_data.pu
plantuml -tpng gpu_inspection_architecture.pu

# 生成SVG图片
plantuml -tsvg inspection.pu
plantuml -tsvg gpu_inspection_state.pu
plantuml -tsvg gpu_inspection_data.pu
plantuml -tsvg gpu_inspection_architecture.pu
```

这些图表提供了GPU巡检系统的全方位视图，有助于理解系统设计、维护代码和进行功能扩展。
